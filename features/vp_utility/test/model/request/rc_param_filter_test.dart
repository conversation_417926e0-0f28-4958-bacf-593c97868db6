import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vp_utility/core/constant/date_filter_constants.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';
import 'package:vp_utility/screen/recommendation_list/enum/recommendation_enum.dart';

void main() {
  group('RcParamFilter', () {
    test('should create filter with default date range', () {
      final filter = RcParamFilter.withDefaultDateRange();
      
      expect(filter.dateTimeRangeCustom, isNotNull);
      expect(
        DateFilterConstants.isDefaultDateRange(filter.dateTimeRangeCustom),
        isTrue,
      );
    });

    test('should identify default filter correctly', () {
      final defaultFilter = RcParamFilter.withDefaultDateRange();
      expect(defaultFilter.isDefault(), isTrue);
      
      final customFilter = RcParamFilter(
        symbol: 'VPB',
        dateTimeRangeCustom: DateTimeRange(
          start: DateTime.now().subtract(const Duration(days: 10)),
          end: DateTime.now(),
        ),
      );
      expect(customFilter.isDefault(), isFalse);
    });

    test('should detect custom date range correctly', () {
      final defaultFilter = RcParamFilter.withDefaultDateRange();
      expect(defaultFilter.hasCustomDateRange(), isFalse);
      
      final customFilter = RcParamFilter(
        dateTimeRangeCustom: DateTimeRange(
          start: DateTime.now().subtract(const Duration(days: 10)),
          end: DateTime.now(),
        ),
      );
      expect(customFilter.hasCustomDateRange(), isTrue);
    });

    test('should return effective date range', () {
      final filterWithoutDate = RcParamFilter();
      final effectiveRange = filterWithoutDate.getEffectiveDateRange();
      expect(
        DateFilterConstants.isDefaultDateRange(effectiveRange),
        isTrue,
      );
      
      final customRange = DateTimeRange(
        start: DateTime.now().subtract(const Duration(days: 5)),
        end: DateTime.now(),
      );
      final filterWithCustomDate = RcParamFilter(
        dateTimeRangeCustom: customRange,
      );
      expect(filterWithCustomDate.getEffectiveDateRange(), equals(customRange));
    });

    test('should handle filter with other parameters', () {
      final filter = RcParamFilter(
        symbol: 'VPB',
        marketTypeInit: MarketType.hose,
        recommendationTypeInit: RecommendationTypeFilter.buy,
        statusTypeInit: RecommendationStatus.active,
        dateTimeRangeCustom: DateFilterConstants.defaultDateRange,
      );
      
      // Should not be default because of other parameters
      expect(filter.isDefault(), isFalse);
      // But should not have custom date range
      expect(filter.hasCustomDateRange(), isFalse);
    });
  });
}
