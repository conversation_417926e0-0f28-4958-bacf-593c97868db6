import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vp_utility/core/constant/date_filter_constants.dart';

void main() {
  group('DateFilterConstants', () {
    test('should return default date range with 1 month duration', () {
      final dateRange = DateFilterConstants.defaultDateRange;
      final now = DateTime.now();
      final expectedStart = DateTime(now.year, now.month - 1, now.day);
      
      expect(dateRange.start.year, expectedStart.year);
      expect(dateRange.start.month, expectedStart.month);
      expect(dateRange.start.day, expectedStart.day);
      expect(dateRange.end.year, now.year);
      expect(dateRange.end.month, now.month);
      expect(dateRange.end.day, now.day);
    });

    test('should create date range with custom months duration', () {
      final dateRange = DateFilterConstants.createDateRange(3);
      final now = DateTime.now();
      final expectedStart = DateTime(now.year, now.month - 3, now.day);
      
      expect(dateRange.start.year, expectedStart.year);
      expect(dateRange.start.month, expectedStart.month);
      expect(dateRange.start.day, expectedStart.day);
    });

    test('should correctly identify default date range', () {
      final defaultRange = DateFilterConstants.defaultDateRange;
      expect(DateFilterConstants.isDefaultDateRange(defaultRange), isTrue);
      
      final customRange = DateTimeRange(
        start: DateTime.now().subtract(const Duration(days: 10)),
        end: DateTime.now(),
      );
      expect(DateFilterConstants.isDefaultDateRange(customRange), isFalse);
      expect(DateFilterConstants.isDefaultDateRange(null), isFalse);
    });

    test('should handle month boundary correctly', () {
      // Test with a date in January to ensure year boundary is handled
      final januaryDate = DateTime(2024, 1, 15);
      final expectedStart = DateTime(2023, 12, 15); // Should go to previous year
      
      // Mock the current date for testing
      final range = DateTimeRange(
        start: DateTime(januaryDate.year, januaryDate.month - 1, januaryDate.day),
        end: januaryDate,
      );
      
      expect(range.start.year, expectedStart.year);
      expect(range.start.month, expectedStart.month);
      expect(range.start.day, expectedStart.day);
    });
  });
}
