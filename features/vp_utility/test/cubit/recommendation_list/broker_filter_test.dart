import 'package:flutter_test/flutter_test.dart';
import 'package:vp_utility/cubit/recommendation_list/broker_filter/broker_filter_cubit.dart';
import 'package:vp_utility/cubit/recommendation_list/broker_filter/broker_filter_state.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';

void main() {
  group('BrokerFilterCubit', () {
    late BrokerFilterCubit brokerFilterCubit;

    setUp(() {
      brokerFilterCubit = BrokerFilterCubit();
    });

    tearDown(() {
      brokerFilterCubit.close();
    });

    test('initial state should be empty', () {
      expect(brokerFilterCubit.state, equals(BrokerFilterState.initial()));
      expect(brokerFilterCubit.state.stockFilter, isNull);
      expect(brokerFilterCubit.state.generalFilter, isNull);
    });

    test('should set stock filter correctly', () {
      // Given
      final filter = RcParamFilter(symbol: 'VPB');

      // When
      brokerFilterCubit.setStockFilter(filter);

      // Then
      expect(brokerFilterCubit.state.stockFilter, equals(filter));
      expect(brokerFilterCubit.state.generalFilter, isNull);
    });

    test('should set general filter correctly', () {
      // Given
      final filter = RcParamFilter(symbol: 'VCB');

      // When
      brokerFilterCubit.setGeneralFilter(filter);

      // Then
      expect(brokerFilterCubit.state.generalFilter, equals(filter));
      expect(brokerFilterCubit.state.stockFilter, isNull);
    });

    test('should set filter by index correctly', () {
      // Given
      final stockFilter = RcParamFilter(symbol: 'VPB');
      final generalFilter = RcParamFilter(symbol: 'VCB');

      // When - Set stock filter (index 0)
      brokerFilterCubit.setFilterByIndex(0, stockFilter);

      // Then
      expect(brokerFilterCubit.state.stockFilter, equals(stockFilter));
      expect(brokerFilterCubit.state.generalFilter, isNull);

      // When - Set general filter (index 1)
      brokerFilterCubit.setFilterByIndex(1, generalFilter);

      // Then
      expect(brokerFilterCubit.state.stockFilter, equals(stockFilter));
      expect(brokerFilterCubit.state.generalFilter, equals(generalFilter));
    });

    test('should clear filters correctly', () {
      // Given
      final stockFilter = RcParamFilter(symbol: 'VPB');
      final generalFilter = RcParamFilter(symbol: 'VCB');
      brokerFilterCubit.setStockFilter(stockFilter);
      brokerFilterCubit.setGeneralFilter(generalFilter);

      // When - Clear stock filter
      brokerFilterCubit.clearStockFilter();

      // Then
      expect(brokerFilterCubit.state.stockFilter, isNull);
      expect(brokerFilterCubit.state.generalFilter, equals(generalFilter));

      // When - Clear general filter
      brokerFilterCubit.clearGeneralFilter();

      // Then
      expect(brokerFilterCubit.state.stockFilter, isNull);
      expect(brokerFilterCubit.state.generalFilter, isNull);
    });

    test('should clear filter by index correctly', () {
      // Given
      final stockFilter = RcParamFilter(symbol: 'VPB');
      final generalFilter = RcParamFilter(symbol: 'VCB');
      brokerFilterCubit.setStockFilter(stockFilter);
      brokerFilterCubit.setGeneralFilter(generalFilter);

      // When - Clear stock filter by index
      brokerFilterCubit.clearFilterByIndex(0);

      // Then
      expect(brokerFilterCubit.state.stockFilter, isNull);
      expect(brokerFilterCubit.state.generalFilter, equals(generalFilter));

      // When - Clear general filter by index
      brokerFilterCubit.clearFilterByIndex(1);

      // Then
      expect(brokerFilterCubit.state.stockFilter, isNull);
      expect(brokerFilterCubit.state.generalFilter, isNull);
    });

    test('should get filter by index correctly', () {
      // Given
      final stockFilter = RcParamFilter(symbol: 'VPB');
      final generalFilter = RcParamFilter(symbol: 'VCB');
      brokerFilterCubit.setStockFilter(stockFilter);
      brokerFilterCubit.setGeneralFilter(generalFilter);

      // When & Then
      expect(brokerFilterCubit.getFilterByIndex(0), equals(stockFilter));
      expect(brokerFilterCubit.getFilterByIndex(1), equals(generalFilter));
      expect(brokerFilterCubit.getFilterByIndex(2), isNull);
    });

    test('should check hasFilter correctly', () {
      // Given - No filters initially
      expect(brokerFilterCubit.hasFilterForIndex(0), isFalse);
      expect(brokerFilterCubit.hasFilterForIndex(1), isFalse);

      // When - Set non-default filter
      final filter = RcParamFilter(symbol: 'VPB');
      brokerFilterCubit.setStockFilter(filter);

      // Then
      expect(brokerFilterCubit.hasFilterForIndex(0), isTrue);
      expect(brokerFilterCubit.hasFilterForIndex(1), isFalse);
    });
  });

  group('Filter State Isolation Test', () {
    late BrokerFilterCubit brokerFilterCubit;

    setUp(() {
      brokerFilterCubit = BrokerFilterCubit();
    });

    tearDown(() {
      brokerFilterCubit.close();
    });

    test('should maintain filter isolation between tabs', () {
      // Given - Set different filters for each tab
      final stockFilter = RcParamFilter(symbol: 'VPB');
      final generalFilter = RcParamFilter(symbol: 'VCB');

      brokerFilterCubit.setFilterByIndex(0, stockFilter);
      brokerFilterCubit.setFilterByIndex(1, generalFilter);

      // Then - Verify filters are correctly isolated
      expect(brokerFilterCubit.getFilterByIndex(0), equals(stockFilter));
      expect(brokerFilterCubit.getFilterByIndex(1), equals(generalFilter));

      // Verify that changing one filter doesn't affect the other
      final newStockFilter = RcParamFilter(symbol: 'TCB');
      brokerFilterCubit.setFilterByIndex(0, newStockFilter);

      expect(brokerFilterCubit.getFilterByIndex(0), equals(newStockFilter));
      expect(brokerFilterCubit.getFilterByIndex(1), equals(generalFilter));
    });

    test('should handle tab reset scenario correctly', () {
      // Given - Set filters for both tabs
      final stockFilter = RcParamFilter(symbol: 'VPB');
      final generalFilter = RcParamFilter(symbol: 'VCB');

      brokerFilterCubit.setFilterByIndex(0, stockFilter);
      brokerFilterCubit.setFilterByIndex(1, generalFilter);

      // When - Simulate tab reset (like when switching back to Broker tab)
      // The filters should remain intact and isolated

      // Then - Verify filters are still correctly maintained
      expect(brokerFilterCubit.getFilterByIndex(0), equals(stockFilter));
      expect(brokerFilterCubit.getFilterByIndex(1), equals(generalFilter));
      expect(brokerFilterCubit.hasFilterForIndex(0), isTrue);
      expect(brokerFilterCubit.hasFilterForIndex(1), isTrue);
    });
  });
}
