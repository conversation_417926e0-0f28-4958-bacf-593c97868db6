import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/recommendation_list/broker_filter/broker_filter_cubit.dart';
import 'package:vp_utility/cubit/recommendation_list/broker_list/broker_list_bloc.dart';
import 'package:vp_utility/cubit/recommendation_list/inv_rc_list/inv_rc_list_cubit.dart';
import 'package:vp_utility/cubit/recommendation_list/recommendation_filter/recommendation_filter_cubit.dart';
import 'package:vp_utility/cubit/recommendation_list/recommendation_filter/recommendation_filter_state.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';
import 'package:vp_utility/screen/recommendation_home/enum/recommendation_type.dart';
import 'package:vp_utility/screen/recommendation_list/broker_list/broker_list.dart';
import 'package:vp_utility/screen/recommendation_list/widget/list_consultant_rc.dart';
import 'package:vp_utility/screen/recommendation_list/widget/list_rc_modal_filter.dart';
import 'package:vp_utility/screen/recommendation_list/widget/list_rc_widget.dart';

class RecommendationList extends StatefulWidget {
  const RecommendationList({super.key, this.recommendationType});
  final RecommendationType? recommendationType;
  @override
  State<RecommendationList> createState() => _RecommendationListState();
}

class _RecommendationListState extends State<RecommendationList>
    with SingleTickerProviderStateMixin {
  bool hasBroker = false;
  late TabController _tabController;
  late List<RecommendationType> tabTypes;

  @override
  void initState() {
    super.initState();
    _checkBroker();
    tabTypes = RecommendationTypeExtention.valuesByHasBroker(hasBroker);
    _tabController = TabController(
      length: tabTypes.length,
      vsync: this,
      initialIndex:
          widget.recommendationType == RecommendationType.broker ? 1 : 0,
    );
    _tabController.addListener(_handleTabChange);
  }

  void _checkBroker() {
    var customerInfo = GetIt.instance<AuthCubit>().customerInfoIam;
    var hasBrokerData = customerInfo?.brokerNo != null;
    setState(() {
      hasBroker = hasBrokerData;
    });
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => InvRecommendationListCubit()..init()),
        BlocProvider(create: (context) => BrokerListCubit()),
        BlocProvider(create: (context) => BrokerFilterCubit()),
        BlocProvider(create: (_) => RecommendationFilterCubit()),
      ],
      child: VPScaffold(
        backgroundColor: vpColor.backgroundElevationMinus1,
        body: SafeArea(
          child: Column(
            children: [
              BlocBuilder<RecommendationFilterCubit, RecommendationFilterState>(
                builder: (context, filterState) {
                  if (filterState.filters.keys == RecommendationType.broker)  
                     print(
                        "------------------------ ************ ${filterState.filters.keys}",
                      );
                  return BlocBuilder<BrokerListCubit, BrokerListState>(
                    buildWhen:
                        (previous, current) =>
                            previous.tabIndex != current.tabIndex,
                    builder: (context, brokerState) {
                      final currentType = tabTypes[_tabController.index];

                      // Kiểm tra hasFilter cho từng loại tab
                      bool hasFilter;
                      if (currentType == RecommendationType.broker) {
                        // Với broker tab, kiểm tra filter của sub-tab hiện tại
                        print(
                          "------------------------ ************ ${brokerState.tabIndex}",
                        );
                        final brokerTabIndex = brokerState.tabIndex;
                        hasFilter = context
                            .read<BrokerFilterCubit>()
                            .hasFilterForIndex(brokerTabIndex);
                      } else {
                        hasFilter = filterState.hasFilter(currentType);
                      }

                      return HeaderWidget(
                        subTitle: "Cổ phiếu",
                        title: "Khuyến nghị đầu tư",
                        actionBack: () {
                          Navigator.pop(context);
                        },
                        actionRight: () {
                          showModalFilter(context, _tabController.index);
                        },
                        icon:
                            hasFilter
                                ? Assets.icons.icHaveFilter.svg()
                                : Assets.icons.icFilter.svg(),
                      );
                    },
                  );
                },
              ),
              VPTabBar(
                controller: _tabController,
                tabs: [...tabTypes.map((type) => Tab(text: type.title))],
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    ListRCWidget(),
                    hasBroker ? BrokerListScreen() : ListConsultanRc(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> showModalFilter(BuildContext context, int tabIndex) async {
    final currentType = tabTypes[tabIndex];

    // Nếu đang ở broker tab, cần lấy filter từ BrokerFilterCubit
    RcParamFilter currentFilter;
    if (currentType == RecommendationType.broker) {
      // Lấy current tab index từ BrokerListScreen với kiểm tra an toàn
      int brokerTabIndex;
      try {
        brokerTabIndex = context.read<BrokerListCubit>().state.tabIndex;
      } catch (e) {
        brokerTabIndex = 0; // Mặc định là tab đầu tiên
      }
      currentFilter =
          context.read<BrokerFilterCubit>().getFilterByIndex(brokerTabIndex) ??
          RcParamFilter();
    } else {
      currentFilter =
          context.read<RecommendationFilterCubit>().getFilter(currentType) ??
          RcParamFilter();
    }

    await showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext contextBottomSheet) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: ListRcModalFilter(
            recommendationType: currentType,
            rcParamFilterInit: currentFilter,
            brokerTabIndex:
                currentType == RecommendationType.broker
                    ? (context.read<BrokerListCubit>().state.tabIndex)
                    : null, // Truyền brokerTabIndex khi là broker tab
            onResetFilter: () {
              if (currentType == RecommendationType.broker) {
                int brokerTabIndex;
                try {
                  brokerTabIndex =
                      context.read<BrokerListCubit>().state.tabIndex;
                } catch (e) {
                  brokerTabIndex = 0;
                }
                context.read<BrokerFilterCubit>().clearFilterByIndex(
                  brokerTabIndex,
                );
              } else {
                context.read<RecommendationFilterCubit>().resetFilter(
                  currentType,
                );
              }
            },
            onApplyFilter: (filter) {
              if (currentType == RecommendationType.broker) {
                int brokerTabIndex;
                try {
                  brokerTabIndex =
                      context.read<BrokerListCubit>().state.tabIndex;
                } catch (e) {
                  brokerTabIndex = 0;
                }
                context.read<BrokerFilterCubit>().setFilterByIndex(
                  brokerTabIndex,
                  filter,
                );
                context.read<BrokerListCubit>().setFilter(filter);
              } else {
                context.read<RecommendationFilterCubit>().setFilter(
                  currentType,
                  filter,
                );
                context.read<InvRecommendationListCubit>().setFilter(
                  filter,
                  currentType,
                );
              }
            },
          ),
        );
      },
    );
  }
}
