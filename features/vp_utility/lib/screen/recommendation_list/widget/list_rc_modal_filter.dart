import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/core/common/date_time_holder_view.dart';
import 'package:vp_utility/core/constant/date_filter_constants.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';
import 'package:vp_utility/screen/recommendation_home/enum/recommendation_type.dart';
import 'package:vp_utility/screen/recommendation_home/widget/rc_one_select_item.dart';
import 'package:vp_utility/screen/recommendation_list/enum/recommendation_enum.dart';

class ListRcModalFilter extends StatefulWidget {
  const ListRcModalFilter({
    super.key,
    required this.rcParamFilterInit,
    this.onResetFilter,
    this.onApplyFilter,
    required this.recommendationType,
    this.brokerTabIndex, // Thêm parameter này
  });

  final RcParamFilter rcParamFilterInit;
  final VoidCallback? onResetFilter;
  final void Function(RcParamFilter filter)? onApplyFilter;
  final RecommendationType recommendationType;
  final int? brokerTabIndex; // Thêm property này

  @override
  State<ListRcModalFilter> createState() => _ListRcModalFilterState();
}

class _ListRcModalFilterState extends State<ListRcModalFilter> {
  late MarketType _marketTypeInit;
  late RecommendationTypeFilter _recommendationTypeInit;
  late RecommendationStatus _statusTypeInit;

  late DateTime _startDate;
  late DateTime _endDate;
  TextEditingController controller = TextEditingController();
  late RecommendationType _recommendationType;

  @override
  void initState() {
    _marketTypeInit = widget.rcParamFilterInit.marketTypeInit ?? MarketType.all;
    _recommendationTypeInit =
        widget.rcParamFilterInit.recommendationTypeInit ??
        RecommendationTypeFilter.all;
    _statusTypeInit =
        widget.rcParamFilterInit.statusTypeInit ?? RecommendationStatus.all;
    _recommendationType = widget.recommendationType;
    if (widget.rcParamFilterInit.dateTimeRangeCustom != null) {
      _startDate = widget.rcParamFilterInit.dateTimeRangeCustom!.start;
      _endDate = widget.rcParamFilterInit.dateTimeRangeCustom!.end;
    } else {
      // Initialize with default 1-month date range instead of current day
      final defaultRange = DateFilterConstants.defaultDateRange;
      _startDate = defaultRange.start;
      _endDate = defaultRange.end;
    }
    controller.text = widget.rcParamFilterInit.symbol ?? '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(children: [buildListFilter()]);
  }

  /*----- Build tim kiem theo tai khoan va theo thoi gian 1 thang -> 6 thang ------*/
  Widget buildListFilter() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),

            // Hiển thị theo loại recommendation type
            if (_recommendationType == RecommendationType.broker) ...[
              // Nếu là broker tab, sử dụng brokerTabIndex được truyền vào
              _buildBrokerTabContent(),
            ] else ...[
              // Các tab khác (vpbanks, consultingExpert) - giữ nguyên như cũ
              VPTextFieldWithClear.small(
                onChanged: (e) {},
                controller: controller,
                hintText: "Tìm kiếm theo mã chứng khoán",
                prefixIcon:
                    (_) => IconButton(
                      onPressed: () {},
                      icon: DesignAssets.icons.icSearch.svg(
                        colorFilter: ColorFilter.mode(
                          vpColor.iconPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
              ),
              const SizedBox(height: 24),
              buildTitle("Thời gian khuyến nghị"),
              const SizedBox(height: 8),
              buildFileterDateTime(),
              const SizedBox(height: 24),
              buildTitle("Sàn"),
              const SizedBox(height: 8),
              buildMutilListMarket(),
              const SizedBox(height: 24),
              buildTitle("Loại khuyến nghị"),
              const SizedBox(height: 8),
              buildListRC(),
              const SizedBox(height: 24),
              buildTitle("Trạng thái"),
              const SizedBox(height: 8),
              buildListRCStatus(),
              const SizedBox(height: 40),
              buildBottomAction(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBrokerTabContent() {
    final isStockRcTab = widget.brokerTabIndex == 0; // Tab đầu tiên là Stock RC
    final isGeneralRcTab =
        widget.brokerTabIndex == 1; // Tab thứ hai là General RC

    if (isGeneralRcTab) {
      // Tab General RC - chỉ hiển thị buildFileterDateTime
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildTitle("Thời gian khuyến nghị"),
          const SizedBox(height: 8),
          buildFileterDateTime(),
          const SizedBox(height: 40),
          buildBottomAction(),
        ],
      );
    } else if (isStockRcTab) {
      // Tab Stock RC - hiển thị search và status
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          VPTextFieldWithClear.small(
            controller: controller,
            hintText: "Tìm kiếm theo mã chứng khoán",
            prefixIcon:
                (_) => IconButton(
                  onPressed: () {},
                  icon: DesignAssets.icons.icSearch.svg(
                    colorFilter: ColorFilter.mode(
                      vpColor.iconPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
          ),
          const SizedBox(height: 24),
          buildTitle("Trạng thái"),
          const SizedBox(height: 8),
          buildListRCStatus(),
          const SizedBox(height: 40),
          buildBottomAction(),
        ],
      );
    } else {
      // Fallback - hiển thị tất cả như cũ
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          VPTextFieldWithClear.small(
            onChanged: (e) {},
            controller: controller,
            hintText: "Tìm kiếm theo mã chứng khoán",
            prefixIcon:
                (_) => IconButton(
                  onPressed: () {},
                  icon: DesignAssets.icons.icSearch.svg(
                    colorFilter: ColorFilter.mode(
                      vpColor.iconPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
          ),
          const SizedBox(height: 24),
          buildTitle("Thời gian khuyến nghị"),
          const SizedBox(height: 8),
          buildFileterDateTime(),
          const SizedBox(height: 24),
          buildTitle("Sàn"),
          const SizedBox(height: 8),
          buildMutilListMarket(),
          const SizedBox(height: 24),
          buildTitle("Loại khuyến nghị"),
          const SizedBox(height: 8),
          buildListRC(),
          const SizedBox(height: 24),
          buildTitle("Trạng thái"),
          const SizedBox(height: 8),
          buildListRCStatus(),
          const SizedBox(height: 40),
          buildBottomAction(),
        ],
      );
    }
  }

  VPDateTimeHolderView buildFileterDateTime() {
    return VPDateTimeHolderView(
      key: ValueKey(
        '${_startDate.millisecondsSinceEpoch}_${_endDate.millisecondsSinceEpoch}',
      ),
      startDate: _startDate,
      endDate: _endDate,
      onDateTimeChanged: (data) {
        // if (data.endDate.difference(data.startDate).inDays.abs() > (6 * 30)) {
        //   VPPopup.oneButton(
        //     title: S.current.money_transfer_attention,
        //     content: "Vui lòng chọn khoảng thời gian nhỏ hơn 6 tháng",
        //   ).icFail.btnClose.showDialog(context);
        // }
        setState(() {
          _startDate = data.startDate;
          _endDate = data.endDate;
        });
      },
    );
  }

  /*---------- Build Title ----------*/
  Text buildTitle(String keyText) {
    return Text(
      keyText,
      style: context.textStyle.subtitle14?.copyWith(
        color: Theme.of(context).black,
      ),
    );
  }

  Widget buildMutilListMarket() {
    return Row(
      spacing: 8.0,
      children:
          MarketType.values.map((type) {
            return RCOneSelectItem<MarketType>(
              value: type,
              selected: _marketTypeInit == type,
              onSelected: (e) {
                setState(() {
                  _marketTypeInit = e;
                });
              },
              displayText: type.label,
            );
          }).toList(),
    );
  }

  /*---------- Bottom action ----------*/
  Widget buildBottomAction() {
    return Row(
      children: [
        Expanded(
          child: VpsButton.secondaryXsSmall(
            title: "Làm mới",
            onPressed: () {
              setState(() {
                _marketTypeInit = MarketType.all;
                _recommendationTypeInit = RecommendationTypeFilter.all;
                _statusTypeInit = RecommendationStatus.all;

                // Reset to default 1-month date range instead of current day
                final defaultRange = DateFilterConstants.defaultDateRange;
                _startDate = defaultRange.start;
                _endDate = defaultRange.end;

                controller.clear();
              });
              widget.onResetFilter?.call();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: VpsButton.primarySmall(
            title: "Áp dụng",
            onPressed: () {
              final isDefaultDateRange = DateFilterConstants.isDefaultDateRange(
                DateTimeRange(start: _startDate, end: _endDate),
              );
              final isDefault =
                  controller.text == '' &&
                  _marketTypeInit == MarketType.all &&
                  _recommendationTypeInit == RecommendationTypeFilter.all &&
                  _statusTypeInit == RecommendationStatus.all &&
                  isDefaultDateRange;
              if (isDefault) {
                widget.onResetFilter?.call();
                widget.onApplyFilter?.call(
                  RcParamFilter.withDefaultDateRange(),
                );
              } else {
                DateTimeRange? dateTimeRangeCustomFiltter;
                // Always include date range if it's not the default
                if (!isDefaultDateRange) {
                  dateTimeRangeCustomFiltter = DateTimeRange(
                    start: _startDate,
                    end: _endDate,
                  );
                }
                widget.onApplyFilter?.call(
                  RcParamFilter(
                    symbol: controller.text.trim().toUpperCase(),
                    marketTypeInit: _marketTypeInit,
                    recommendationTypeInit: _recommendationTypeInit,
                    statusTypeInit: _statusTypeInit,
                    dateTimeRangeCustom: dateTimeRangeCustomFiltter,
                  ),
                );
              }
              Navigator.pop(context);
            },
          ),
        ),
      ],
    );
  }

  buildListRC() {
    return Wrap(
      spacing: 8.0,
      children:
          RecommendationTypeFilter.values.map((type) {
            return RCOneSelectItem<RecommendationTypeFilter>(
              value: type,
              selected: _recommendationTypeInit == type,
              onSelected: (e) {
                setState(() {
                  _recommendationTypeInit = e;
                });
              },
              displayText: type.label,
            );
          }).toList(),
    );
  }

  buildListRCStatus() {
    return Wrap(
      spacing: 8.0,
      children:
          RecommendationStatus.values.map((type) {
            return RCOneSelectItem<RecommendationStatus>(
              value: type,
              selected: _statusTypeInit == type,
              onSelected: (e) {
                setState(() {
                  _statusTypeInit = e;
                });
              },
              displayText: type.label,
            );
          }).toList(),
    );
  }
}
