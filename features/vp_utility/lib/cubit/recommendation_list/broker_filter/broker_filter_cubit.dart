import 'package:vp_core/vp_core.dart';
import 'package:vp_utility/cubit/recommendation_list/broker_filter/broker_filter_state.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';

class BrokerFilterCubit extends Cubit<BrokerFilterState> {
  BrokerFilterCubit() : super(BrokerFilterState.initial());

  void setStockFilter(RcParamFilter filter) {
    emit(state.copyWith(stockFilter: filter));
  }

  void setGeneralFilter(RcParamFilter filter) {
    emit(state.copyWith(generalFilter: filter));
  }

  void setFilterByIndex(int tabIndex, RcParamFilter filter) {
    if (tabIndex == 0) {
      setStockFilter(filter);
    } else {
      setGeneralFilter(filter);
    }
  }

  void clearStockFilter() {
    emit(state.copyWith(stockFilter: null));
  }

  void clearGeneralFilter() {
    emit(state.copyWith(generalFilter: null));
  }

  void clearFilterByIndex(int tabIndex) {
    if (tabIndex == 0) {
      clearStockFilter();
    } else {
      clearGeneralFilter();
    }
  }

  void resetStockFilter() {
    clearStockFilter();
  }

  void resetGeneralFilter() {
    clearGeneralFilter();
  }

  void resetFilterByIndex(int tabIndex) {
    clearFilterByIndex(tabIndex);
  }

  RcParamFilter? getFilterByIndex(int tabIndex) {
    final filter = state.getFilterByIndex(tabIndex);
    // Return default filter if none exists for this tab
    return filter ?? RcParamFilter.withDefaultDateRange();
  }

  bool hasFilterForIndex(int tabIndex) {
    return state.hasFilterForIndex(tabIndex);
  }
}
