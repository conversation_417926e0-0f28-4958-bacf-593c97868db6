import 'package:equatable/equatable.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';

class BrokerFilterState extends Equatable {
  const BrokerFilterState({this.stockFilter, this.generalFilter});

  final RcParamFilter? stockFilter;
  final RcParamFilter? generalFilter;

  factory BrokerFilterState.initial() => BrokerFilterState(
    stockFilter: RcParamFilter.withDefaultDateRange(),
    generalFilter: RcParamFilter.withDefaultDateRange(),
  );

  BrokerFilterState copyWith({
    RcParamFilter? stockFilter,
    RcParamFilter? generalFilter,
  }) {
    return BrokerFilterState(
      stockFilter: stockFilter ?? this.stockFilter,
      generalFilter: generalFilter ?? this.generalFilter,
    );
  }

  @override
  List<Object?> get props => [stockFilter, generalFilter];
}

extension BrokerFilterStateExtension on BrokerFilterState {
  RcParamFilter? getFilterByIndex(int tabIndex) {
    return tabIndex == 0 ? stockFilter : generalFilter;
  }

  bool hasFilterForIndex(int tabIndex) {
    final filter = getFilterByIndex(tabIndex);
    if (filter == null) return false;
    return !filter.isDefault();
  }
}
