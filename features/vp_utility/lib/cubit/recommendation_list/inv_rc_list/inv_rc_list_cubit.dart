import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_utility/core/repository/recommendation_repository.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';
import 'package:vp_utility/model/response/recommendation/recommendation_info_model.dart';
import 'package:vp_utility/screen/recommendation_home/enum/recommendation_type.dart';

part 'inv_rc_list_cubit.freezed.dart';
part 'inv_rc_list_state.dart';

class InvRecommendationListCubit extends Cubit<InvRecommendationListState> {
  InvRecommendationListCubit() : super(InvRecommendationListState());
  final RecommendationRepository _recommendationRepository =
      GetIt.instance<RecommendationRepository>();

  bool _hasMoreVpBanks = true;
  bool _hasMoreConsultant = true;

  Future<void> getData({int? pageNo, bool isRefresh = false}) async {
    try {
      final int currentPage = pageNo ?? (isRefresh ? 0 : state.pageNoVpBanks);
      if (isRefresh) {
        _hasMoreVpBanks = true;
      }
      final result = await _recommendationRepository.getStockRecommendation(
        RcRequestParam(
          pageNo: currentPage,
          pageSize: 20,
          dept: RecommendationType.vpbanks.dataServer,
          //Sàn
          exchangeCode: state.rcParamFilterVpBanks?.marketTypeInit?.toRequest(),
          //Loại khuyến nghị
          typeRc:
              state.rcParamFilterVpBanks?.recommendationTypeInit?.toRequest(),
          //Trạng thái
          statusRc: state.rcParamFilterVpBanks?.statusTypeInit?.toRequest(),
          fromDate: AppTimeUtils.format(
            state.rcParamFilterVpBanks?.getEffectiveDateRange().start,
            AppTimeUtilsFormat.dateYMD,
          ),
          toDate: AppTimeUtils.format(
            state.rcParamFilterVpBanks?.getEffectiveDateRange().end,
            AppTimeUtilsFormat.dateYMD,
          ),
          symbols: state.rcParamFilterVpBanks?.symbol?.toUpperCase(),
        ),
      );
      List<RecommendationInfoModel> newData =
          (result.data ?? []).cast<RecommendationInfoModel>();
      List<RecommendationInfoModel> allData =
          isRefresh || currentPage == 0 ? newData : [...state.data, ...newData];
      _hasMoreVpBanks = newData.length == 20;
      emit(state.copyWith(data: allData, pageNoVpBanks: currentPage));
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
    }
  }

  Future<void> getDataConsultant({int? pageNo, bool isRefresh = false}) async {
    try {
      final int currentPage =
          pageNo ?? (isRefresh ? 0 : state.pageNoConsultant);
      if (isRefresh) {
        _hasMoreConsultant = true;
      }
      final result = await _recommendationRepository.getStockRecommendation(
        RcRequestParam(
          pageNo: currentPage,
          pageSize: 20,
          dept: RecommendationType.consultingExpert.dataServer,
          //Sàn
          exchangeCode:
              state.rcParamFilterConsultant?.marketTypeInit?.toRequest(),
          //Loại khuyến nghị
          typeRc:
              state.rcParamFilterConsultant?.recommendationTypeInit
                  ?.toRequest(),
          //Trạng thái
          statusRc: state.rcParamFilterConsultant?.statusTypeInit?.toRequest(),
          fromDate: AppTimeUtils.format(
            state.rcParamFilterConsultant?.getEffectiveDateRange().start,
            AppTimeUtilsFormat.dateYMD,
          ),
          toDate: AppTimeUtils.format(
            state.rcParamFilterConsultant?.getEffectiveDateRange().end,
            AppTimeUtilsFormat.dateYMD,
          ),
          symbols: state.rcParamFilterVpBanks?.symbol?.toUpperCase(),
        ),
      );
      List<RecommendationInfoModel> newData =
          (result.data ?? []).cast<RecommendationInfoModel>();
      List<RecommendationInfoModel> allData =
          isRefresh || currentPage == 0
              ? newData
              : [...state.dataConsultant, ...newData];
      _hasMoreConsultant = newData.length == 20;
      emit(
        state.copyWith(dataConsultant: allData, pageNoConsultant: currentPage),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
    }
  }

  Future<void> refreshData(RecommendationType type) async {
    emit(state.copyWith(isLoading: true));
    if (type == RecommendationType.vpbanks) {
      await getData(pageNo: 0, isRefresh: true);
    } else {
      await getDataConsultant(pageNo: 0, isRefresh: true);
    }
    emit(state.copyWith(isLoading: false));
  }

  Future<void> loadMoreData({bool isConsultant = false}) async {
    if (state.isLoadingMore) return;
    if (isConsultant) {
      if (!_hasMoreConsultant) return;
      emit(state.copyWith(isLoadingMore: true));
      final nextPage = state.pageNoConsultant + 1;
      await getDataConsultant(pageNo: nextPage);
      emit(state.copyWith(isLoadingMore: false));
    } else {
      if (!_hasMoreVpBanks) return;
      emit(state.copyWith(isLoadingMore: true));
      final nextPage = state.pageNoVpBanks + 1;
      await getData(pageNo: nextPage);
      emit(state.copyWith(isLoadingMore: false));
    }
  }

  void init() async {
    emit(state.copyWith(isLoading: true));
    await Future.wait([
      getData(pageNo: 0, isRefresh: true),
      getDataConsultant(pageNo: 0, isRefresh: true),
    ]);
    emit(state.copyWith(isLoading: false));
  }

  // thêm hàm filter
  void setFilter(RcParamFilter filter, RecommendationType type) {
    if (type == RecommendationType.vpbanks) {
      emit(state.copyWith(rcParamFilterVpBanks: filter));
      getData(pageNo: 0, isRefresh: true);
    } else {
      emit(state.copyWith(rcParamFilterConsultant: filter));
      getDataConsultant(pageNo: 0, isRefresh: true);
    }
  }
}
