import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_utility/cubit/recommendation_list/recommendation_filter/recommendation_filter_state.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';
import 'package:vp_utility/screen/recommendation_home/enum/recommendation_type.dart';

class RecommendationFilterCubit extends Cubit<RecommendationFilterState> {
  RecommendationFilterCubit() : super(RecommendationFilterState()) {
    _initializeDefaultFilters();
  }

  /// Initialize default date range filters for all recommendation types
  void _initializeDefaultFilters() {
    final defaultFilters = <RecommendationType, RcParamFilter>{};

    // Initialize with default date range for VPBanks and Consultant Expert
    defaultFilters[RecommendationType.vpbanks] =
        RcParamFilter.withDefaultDateRange();
    defaultFilters[RecommendationType.consultingExpert] =
        RcParamFilter.withDefaultDateRange();

    emit(state.copyWith(filters: defaultFilters));
  }

  void setFilter(RecommendationType type, RcParamFilter filter) {
    final newFilters = Map<RecommendationType, RcParamFilter>.from(
      state.filters,
    );
    newFilters[type] = filter;
    emit(state.copyWith(filters: newFilters));
  }

  void clearFilter(RecommendationType type) {
    final newFilters = Map<RecommendationType, RcParamFilter>.from(
      state.filters,
    );
    newFilters.remove(type);
    emit(state.copyWith(filters: newFilters));
  }

  void resetFilter(RecommendationType type) {
    final newFilters = Map<RecommendationType, RcParamFilter>.from(
      state.filters,
    );
    newFilters.remove(type);
    emit(state.copyWith(filters: newFilters));
  }

  bool hasFilter(RecommendationType type) => state.hasFilter(type);

  RcParamFilter? getFilter(RecommendationType type) {
    final filter = state.getFilter(type);
    // Return default filter if none exists for this type
    return filter ?? RcParamFilter.withDefaultDateRange();
  }
}
