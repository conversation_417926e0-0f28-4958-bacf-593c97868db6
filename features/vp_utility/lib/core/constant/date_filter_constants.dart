import 'package:flutter/material.dart';

/// Constants for date filter functionality in recommendation screens
class DateFilterConstants {
  /// Default duration for date range filter (in months)
  /// This can be easily modified to change the default behavior across the app
  static const int defaultMonthsDuration = 1;

  /// Creates a default date range from [defaultMonthsDuration] months ago to today
  static DateTimeRange get defaultDateRange {
    final now = DateTime.now();
    final startDate = DateTime(
      now.year,
      now.month - defaultMonthsDuration,
      now.day,
    );
    return DateTimeRange(
      start: startDate,
      end: now,
    );
  }

  /// Creates a date range with specified months duration from today
  static DateTimeRange createDateRange(int monthsDuration) {
    final now = DateTime.now();
    final startDate = DateTime(
      now.year,
      now.month - monthsDuration,
      now.day,
    );
    return DateTimeRange(
      start: startDate,
      end: now,
    );
  }

  /// Checks if a given date range matches the default date range
  /// (within the same day, allowing for time differences)
  static bool isDefaultDateRange(DateTimeRange? dateRange) {
    if (dateRange == null) return false;
    
    final defaultRange = defaultDateRange;
    return _isSameDay(dateRange.start, defaultRange.start) &&
           _isSameDay(dateRange.end, defaultRange.end);
  }

  /// Helper method to check if two dates are on the same day
  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}
