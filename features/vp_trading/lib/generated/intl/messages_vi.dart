// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "trading_all": MessageLookupByLibrary.simpleMessage("Tất cả"),
    "trading_apply": MessageLookupByLibrary.simpleMessage("Áp dụng"),
    "trading_available_stock_volume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng CK khả dụng",
    ),
    "trading_average_cost_price": MessageLookupByLibrary.simpleMessage(
      "Giá vốn trung bình",
    ),
    "trading_average_matching_price": MessageLookupByLibrary.simpleMessage(
      "Giá khớp trung bình",
    ),
    "trading_bid_price_title": MessageLookupByLibrary.simpleMessage(
      "Giá đặt/\nGiá khớp",
    ),
    "trading_buy": MessageLookupByLibrary.simpleMessage("Mua"),
    "trading_buy_order": MessageLookupByLibrary.simpleMessage("Lệnh mua"),
    "trading_buy_order_title": MessageLookupByLibrary.simpleMessage("Lệnh mua"),
    "trading_buying_power": MessageLookupByLibrary.simpleMessage("Sức mua"),
    "trading_cancel_all_order": MessageLookupByLibrary.simpleMessage(
      "Huỷ tất cả",
    ),
    "trading_cancel_order": MessageLookupByLibrary.simpleMessage("Hủy lệnh"),
    "trading_cancel_order_confirm_message":
        MessageLookupByLibrary.simpleMessage(
          "Bạn xác nhận muốn huỷ lệnh đã chọn",
        ),
    "trading_cancel_order_success": MessageLookupByLibrary.simpleMessage(
      "Huỷ lệnh thành công",
    ),
    "trading_cancel_order_title": MessageLookupByLibrary.simpleMessage(
      "Huỷ lệnh",
    ),
    "trading_cancelled": MessageLookupByLibrary.simpleMessage("Đã hủy"),
    "trading_cancelled_2": MessageLookupByLibrary.simpleMessage("Lệnh huỷ"),
    "trading_cancelling": MessageLookupByLibrary.simpleMessage("Đang huỷ"),
    "trading_capital_value": MessageLookupByLibrary.simpleMessage(
      "Giá trị vốn",
    ),
    "trading_category": MessageLookupByLibrary.simpleMessage("Danh mục"),
    "trading_close": MessageLookupByLibrary.simpleMessage("Đóng"),
    "trading_code_title": MessageLookupByLibrary.simpleMessage("Mã"),
    "trading_command_history": MessageLookupByLibrary.simpleMessage("Sổ lệnh"),
    "trading_command_status": MessageLookupByLibrary.simpleMessage(
      "Trạng thái lệnh",
    ),
    "trading_command_type": MessageLookupByLibrary.simpleMessage("Loại lệnh"),
    "trading_cost_price": MessageLookupByLibrary.simpleMessage("Giá vốn"),
    "trading_custom": MessageLookupByLibrary.simpleMessage("Tùy chọn"),
    "trading_deposit": MessageLookupByLibrary.simpleMessage("TK ký quỹ"),
    "trading_deposit_full": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản ký quỹ",
    ),
    "trading_edit_order": MessageLookupByLibrary.simpleMessage("Sửa lệnh"),
    "trading_edit_order_button": MessageLookupByLibrary.simpleMessage(
      "Sửa lệnh",
    ),
    "trading_edited": MessageLookupByLibrary.simpleMessage("Đã sửa"),
    "trading_expected_profit_loss": MessageLookupByLibrary.simpleMessage(
      "Lãi/lỗ dự kiến",
    ),
    "trading_expire": MessageLookupByLibrary.simpleMessage("Hết hạn"),
    "trading_expired": MessageLookupByLibrary.simpleMessage("Hết hiệu lực"),
    "trading_history_stock_code": MessageLookupByLibrary.simpleMessage(
      "Mã cổ phiếu",
    ),
    "trading_holding_portfolio": MessageLookupByLibrary.simpleMessage(
      "Danh mục nắm giữ",
    ),
    "trading_investment_principal": MessageLookupByLibrary.simpleMessage(
      "Gốc đầu tư",
    ),
    "trading_joint_volume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng khớp",
    ),
    "trading_market_value": MessageLookupByLibrary.simpleMessage(
      "Giá trị thị trường",
    ),
    "trading_match_all": MessageLookupByLibrary.simpleMessage("Khớp hết"),
    "trading_matched_complete": MessageLookupByLibrary.simpleMessage(
      "Khớp hết",
    ),
    "trading_matched_partial": MessageLookupByLibrary.simpleMessage(
      "Khớp 1 phần",
    ),
    "trading_matched_price": MessageLookupByLibrary.simpleMessage("Giá khớp"),
    "trading_max_volume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng mua tối đa",
    ),
    "trading_max_volume_sell": MessageLookupByLibrary.simpleMessage(
      "Khối lượng bán tối đa",
    ),
    "trading_modified": MessageLookupByLibrary.simpleMessage("Đã sửa"),
    "trading_modifying": MessageLookupByLibrary.simpleMessage("Đang sửa"),
    "trading_month": MessageLookupByLibrary.simpleMessage("tháng"),
    "trading_no_data": MessageLookupByLibrary.simpleMessage(
      "Hiện tại không có dữ liệu",
    ),
    "trading_no_data_message": MessageLookupByLibrary.simpleMessage(
      "Không có dữ liệu",
    ),
    "trading_no_filter": MessageLookupByLibrary.simpleMessage(
      "Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại",
    ),
    "trading_normal": MessageLookupByLibrary.simpleMessage("Lệnh thường"),
    "trading_order_command_description": MessageLookupByLibrary.simpleMessage(
      "Lệnh được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt",
    ),
    "trading_order_description_atc": MessageLookupByLibrary.simpleMessage(
      "Lệnh mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa",
    ),
    "trading_order_description_ato": MessageLookupByLibrary.simpleMessage(
      "Lệnh tranh mua bán tại mức giá mở cửa",
    ),
    "trading_order_description_buyin": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00",
    ),
    "trading_order_description_gtc": MessageLookupByLibrary.simpleMessage(
      "Lệnh được kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập",
    ),
    "trading_order_description_lo": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt mua - bán chứng khoán theo giá mong muốn",
    ),
    "trading_order_description_mak": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh",
    ),
    "trading_order_description_mok": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập",
    ),
    "trading_order_description_mp": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt mua - bán chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch",
    ),
    "trading_order_description_mtl": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO",
    ),
    "trading_order_description_plo": MessageLookupByLibrary.simpleMessage(
      "Lệnh mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC",
    ),
    "trading_order_price": MessageLookupByLibrary.simpleMessage("Giá đặt lệnh"),
    "trading_order_time": MessageLookupByLibrary.simpleMessage(
      "Thời gian đặt lệnh",
    ),
    "trading_order_type_atc": MessageLookupByLibrary.simpleMessage("Lệnh ATC"),
    "trading_order_type_ato": MessageLookupByLibrary.simpleMessage("Lệnh ATO"),
    "trading_order_type_buyin": MessageLookupByLibrary.simpleMessage(
      "Lệnh Buy-in",
    ),
    "trading_order_type_condition": MessageLookupByLibrary.simpleMessage(
      "Lệnh điều kiện",
    ),
    "trading_order_type_gtc": MessageLookupByLibrary.simpleMessage("Lệnh GTC"),
    "trading_order_type_lo": MessageLookupByLibrary.simpleMessage(
      "Lệnh thường",
    ),
    "trading_order_type_mak": MessageLookupByLibrary.simpleMessage("Lệnh MAK"),
    "trading_order_type_mok": MessageLookupByLibrary.simpleMessage("Lệnh MOK"),
    "trading_order_type_mp": MessageLookupByLibrary.simpleMessage("Lệnh MP"),
    "trading_order_type_mtl": MessageLookupByLibrary.simpleMessage("Lệnh MTL"),
    "trading_order_type_plo": MessageLookupByLibrary.simpleMessage("Lệnh PLO"),
    "trading_order_value": MessageLookupByLibrary.simpleMessage("Giá trị lệnh"),
    "trading_order_value_2": MessageLookupByLibrary.simpleMessage(
      "Giá trị lệnh đặt",
    ),
    "trading_order_value_title": MessageLookupByLibrary.simpleMessage(
      "Giá trị lệnh",
    ),
    "trading_ordinary": MessageLookupByLibrary.simpleMessage("TK thường"),
    "trading_ordinary_full": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản thường",
    ),
    "trading_party_trade": MessageLookupByLibrary.simpleMessage("Đã khớp"),
    "trading_pending_order": MessageLookupByLibrary.simpleMessage(
      "Danh sách lệnh chờ khớp",
    ),
    "trading_pending_rights": MessageLookupByLibrary.simpleMessage(
      "Quyền chờ về",
    ),
    "trading_pending_stock": MessageLookupByLibrary.simpleMessage("CK chờ về"),
    "trading_place_order_price": MessageLookupByLibrary.simpleMessage("Giá"),
    "trading_place_stock_order": MessageLookupByLibrary.simpleMessage(
      "Đặt lệnh cổ phiếu",
    ),
    "trading_portfolio_weight": MessageLookupByLibrary.simpleMessage(
      "Tỉ trọng trong danh mục",
    ),
    "trading_price": MessageLookupByLibrary.simpleMessage("Giá"),
    "trading_price_list": MessageLookupByLibrary.simpleMessage("Bảng giá"),
    "trading_price_price": MessageLookupByLibrary.simpleMessage("Giá khớp"),
    "trading_processing": MessageLookupByLibrary.simpleMessage("Đang xử lý"),
    "trading_rejected": MessageLookupByLibrary.simpleMessage("Từ chối"),
    "trading_reset": MessageLookupByLibrary.simpleMessage("Đặt lại"),
    "trading_restricted_stock": MessageLookupByLibrary.simpleMessage(
      "CK bị hạn chế",
    ),
    "trading_sCode": MessageLookupByLibrary.simpleMessage("Mã CK"),
    "trading_save_command": MessageLookupByLibrary.simpleMessage(
      "Lưu thông tin lệnh cho lần sau",
    ),
    "trading_search_by_stock_code": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm theo mã chứng khoán",
    ),
    "trading_selected": MessageLookupByLibrary.simpleMessage("Đã chọn"),
    "trading_sell": MessageLookupByLibrary.simpleMessage("Bán"),
    "trading_sell_order": MessageLookupByLibrary.simpleMessage("Lệnh bán"),
    "trading_sending": MessageLookupByLibrary.simpleMessage("Đang gửi"),
    "trading_sent": MessageLookupByLibrary.simpleMessage("Đã gửi"),
    "trading_showMessageNextTime": MessageLookupByLibrary.simpleMessage(
      "Hiển thị thông báo này trong lần sau",
    ),
    "trading_status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "trading_status_all": MessageLookupByLibrary.simpleMessage(
      "Trạng thái: Tất cả",
    ),
    "trading_status_filter_title": MessageLookupByLibrary.simpleMessage(
      "Trạng thái",
    ),
    "trading_status_hint": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "trading_status_selected": MessageLookupByLibrary.simpleMessage(
      "Trạng thái: Đã chọn",
    ),
    "trading_status_title": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "trading_stock": MessageLookupByLibrary.simpleMessage("Cổ phiếu"),
    "trading_stock_code": MessageLookupByLibrary.simpleMessage("Mã CK"),
    "trading_stock_symbol": MessageLookupByLibrary.simpleMessage(
      "Mã chứng khoán",
    ),
    "trading_stock_type": MessageLookupByLibrary.simpleMessage("Lệnh"),
    "trading_stop_loss": MessageLookupByLibrary.simpleMessage("Cắt lỗ"),
    "trading_sub_account": MessageLookupByLibrary.simpleMessage("Tiểu khoản"),
    "trading_sub_account_hint": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản",
    ),
    "trading_sub_command": MessageLookupByLibrary.simpleMessage("Lệnh con"),
    "trading_take_profit": MessageLookupByLibrary.simpleMessage("Chốt lời"),
    "trading_take_profit_command_description": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT",
    ),
    "trading_time": MessageLookupByLibrary.simpleMessage("Thời gian"),
    "trading_total_stock_volume": MessageLookupByLibrary.simpleMessage(
      "Tổng khối lượng CK",
    ),
    "trading_transaction_history": MessageLookupByLibrary.simpleMessage(
      "Lịch sử GD",
    ),
    "trading_transaction_type": MessageLookupByLibrary.simpleMessage(
      "Loại giao dịch",
    ),
    "trading_update_order_success": MessageLookupByLibrary.simpleMessage(
      "Cập nhật lệnh thành công",
    ),
    "trading_utilities": MessageLookupByLibrary.simpleMessage("Tiện ích"),
    "trading_value": MessageLookupByLibrary.simpleMessage("Giá trị"),
    "trading_vol_title": MessageLookupByLibrary.simpleMessage(
      "KL đặt/\nKL khớp",
    ),
    "trading_volume": MessageLookupByLibrary.simpleMessage("Khối lượng"),
    "trading_volume_title": MessageLookupByLibrary.simpleMessage("KL"),
    "trading_waiting": MessageLookupByLibrary.simpleMessage("Đang chờ"),
    "trading_waiting_command": MessageLookupByLibrary.simpleMessage("Lệnh chờ"),
    "trading_waiting_match": MessageLookupByLibrary.simpleMessage("Lệnh chờ"),
    "trading_waiting_send": MessageLookupByLibrary.simpleMessage("Chờ gửi"),
    "trading_waiting_to_send": MessageLookupByLibrary.simpleMessage("Chờ gửi"),
    "trading_year": MessageLookupByLibrary.simpleMessage("năm"),
    "traing_stop_loss_command_description": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT",
    ),
  };
}
