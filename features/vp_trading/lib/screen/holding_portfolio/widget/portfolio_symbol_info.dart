
import 'package:flutter/material.dart';
import 'package:vp_common/utils/format_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/widgets/vp_close_price_item_view.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';

class PortfolioSymbolInfo extends StatelessWidget {
  final HoldingPortfolioStockModel item;

  const PortfolioSymbolInfo({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          item.customSymbol,
          style: context.textStyle.subtitle14?.copyWith(
            color: vpColor.textPrimary,
          ),
        ),
        const SizedBox(height: 2),
        IgnorePointer(
          child: VPClosePriceItemView(
            textAlign: TextAlign.start,
            alignment: Alignment.centerLeft,
            symbol: item.symbol ?? "-",
            initClosePrice: item.priceMarketPrice,
            initTextColor:
                item.isBassicPrice ? vpColor.textPrimary : item.colorPrice,
            styleBuilder: (closePrice, color) {
              return vpTextStyle.captionMedium.copyColor(
                color ?? vpColor.textPrimary,
              );
            },
            priceBuilder: (closePrice) => _buildPriceText(closePrice),
          ),
        ),
      ],
    );
  }

  String _buildPriceText(num? closePrice) {
    if (closePrice == null) return '-.-';

    var price = FormatUtils.formatClosePrice(closePrice);
    var percentChange =
        item.refPrice != null && item.refPrice != 0
            ? ((closePrice - (item.refPrice ?? 0)) / (item.refPrice ?? 1) * 100)
            : 0.0;
    var percent = FormatUtils.formatPercent(percentChange, showSign: true);
    return "$price ($percent)";
  }
}
