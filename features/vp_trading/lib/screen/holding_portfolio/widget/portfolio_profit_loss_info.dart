import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/holding_portfolio/table/holding_portfolio_table_cubit.dart';
import 'package:vp_trading/cubit/holding_portfolio/table/holding_portfolio_table_state.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';

class PortfolioProfitLossInfo extends StatelessWidget {
  final HoldingPortfolioStockModel item;

  const PortfolioProfitLossInfo({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      HoldingPortfolioTableCubit,
      HoldingPortfolioTableState,
      ProfitLossDisplayMode
    >(
      selector: (state) => state.profitLossDisplayMode,
      builder: (context, profitLossDisplayMode) {
        final isAbsoluteMode =
            profitLossDisplayMode == ProfitLossDisplayMode.absolute;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildPrimaryRow(context, isAbsoluteMode),
            if (isAbsoluteMode) _buildSecondaryRow(context),
          ],
        );
      },
    );
  }

  Widget _buildPrimaryRow(BuildContext context, bool isAbsoluteMode) {
    final text =
        isAbsoluteMode
            ? "${item.pnlRateDirectionSymbol} ${item.pnlView ?? '-'}"
            : "${item.pnlRateDirectionSymbol} ${item.pnlRateView ?? '-'}";

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!isAbsoluteMode) _buildPnlDirectionSymbol(),
        Flexible(
          child: _buildStyledText(context, text, context.textStyle.subtitle14),
        ),
      ],
    );
  }

  Widget _buildSecondaryRow(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildPnlDirectionSymbol(),
        _buildStyledText(
          context,
          item.pnlRateView ?? '-',
          context.textStyle.body14,
        ),
      ],
    );
  }

  Widget _buildStyledText(
    BuildContext context,
    String text,
    TextStyle? baseStyle,
  ) {
    return AutoSizeText(
      text,
      style: baseStyle?.copyWith(color: item.colorPnl),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      minFontSize: 10,
    );
  }

  Widget _buildPnlDirectionSymbol() {
    if (item.pnlView == null || item.pnlValue == 0) {
      return const SizedBox.shrink();
    }

    return Icon(
      item.pnlValue > 0 ? Icons.arrow_drop_up : Icons.arrow_drop_down,
      color: item.colorPnl,
      size: 24,
    );
  }
}
