import 'package:flutter/material.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/holding_portfolio/list_holding_portfolio/list_holding_portfolio_cubit.dart';
import 'package:vp_trading/cubit/holding_portfolio/table/holding_portfolio_table_cubit.dart';
import 'package:vp_trading/cubit/holding_portfolio/table/holding_portfolio_table_state.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';
import 'package:vp_trading/screen/holding_portfolio/widget/bottom_sheet_holding_portfolio.dart';
import 'package:vp_trading/screen/holding_portfolio/widget/portfolio_profit_loss_info.dart';
import 'package:vp_trading/screen/holding_portfolio/widget/portfolio_symbol_info.dart';

class ContentholdingPortfolio extends StatelessWidget {
  const ContentholdingPortfolio({super.key, this.portfolioList});
  final List<HoldingPortfolioStockModel>? portfolioList;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HoldingPortfolioTableCubit, HoldingPortfolioTableState>(
      builder: (context, tableState) {
        final tableCubit = context.read<HoldingPortfolioTableCubit>();
        final sortedList = tableCubit.sortPortfolioList(portfolioList ?? []);

        return Column(
          children: [
            _buildHeader(context),
            Expanded(
              child: ListView.separated(
                padding: EdgeInsets.zero,
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: sortedList.length,
                itemBuilder: (context, index) {
                  final item = sortedList[index];
                  return _buildDataRow(context, item);
                },
                separatorBuilder:
                    (context, index) => DividerWidget(color: themeData.divider),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return SizedBox(
      height: 50,

      child: Row(
        children: [
          SizedBox(
            width: 90,
            child: _buildSortableHeader(
              context,
              VPTradingLocalize.current.trading_stock_code,
              SortColumn.stockCode,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Align(
              alignment: Alignment.centerRight,
              child: _buildSortableHeader(
                context,
                VPTradingLocalize.current.trading_cost_price,
                SortColumn.costPrice,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerRight,
              child: _buildSortableHeader(
                context,
                VPTradingLocalize.current.trading_volume_title,
                SortColumn.quantity,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 5,
            child: Align(
              alignment: Alignment.centerRight,
              child: _buildProfitLossHeader(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataRow(BuildContext context, HoldingPortfolioStockModel item) {
    return InkWell(
      onTap: () => _openDetailPortfolio(context, item),
      child: SizedBox(
        height: 60,

        child: Row(
          children: [
            SizedBox(
              width: 90,
              child: Align(
                alignment: Alignment.centerLeft,
                child: PortfolioSymbolInfo(item: item),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 2,
              child: Align(
                alignment: Alignment.centerRight,
                child: Text(
                  item.formatPriceView ?? "-",
                  style: context.textStyle.subtitle14?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 3,
              child: Align(
                alignment: Alignment.centerRight,
                child: Text(
                  MoneyUtils.formatMoney(
                    (item.total ?? 0).toDouble(),
                    suffix: '',
                  ),
                  style: context.textStyle.body14?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 5,
              child: Align(
                alignment: Alignment.centerRight,
                child: PortfolioProfitLossInfo(item: item),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openDetailPortfolio(
    BuildContext context,
    HoldingPortfolioStockModel item,
  ) {
    VPPopup.bottomSheet(
      BottomSheetHoldingPortfolio(
        item: item,
        subAccountFilter:
            context.read<ListHoldingPortfolioCubit>().state.subAccount,
        marketValueCategory:
            context.read<ListHoldingPortfolioCubit>().marketValueTotal,
      ),
    ).showSheet(context);
  }

  Widget _buildSortableHeader(
    BuildContext context,
    String title,
    SortColumn column,
  ) {
    return BlocBuilder<HoldingPortfolioTableCubit, HoldingPortfolioTableState>(
      builder: (context, state) {
        final isCurrentColumn = state.sortColumn == column;
        final sortDirection =
            isCurrentColumn ? state.sortDirection : SortDirection.none;

        return GestureDetector(
          onTap:
              () => context.read<HoldingPortfolioTableCubit>().onColumnSort(
                column,
              ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: AutoSizeText(
                  title,
                  style: context.textStyle.captionRegular?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                  maxLines: 1,
                  minFontSize: 10,
                ),
              ),
              const SizedBox(width: 4),
              _buildSortIcon(direction: sortDirection),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProfitLossHeader(BuildContext context) {
    return BlocBuilder<HoldingPortfolioTableCubit, HoldingPortfolioTableState>(
      builder: (context, state) {
        final isCurrentColumn = state.sortColumn == SortColumn.profitLoss;
        final sortDirection =
            isCurrentColumn ? state.sortDirection : SortDirection.none;

        return GestureDetector(
          onTap:
              () => context.read<HoldingPortfolioTableCubit>().onColumnSort(
                SortColumn.profitLoss,
              ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Toggle icon for switching between absolute and percentage
              GestureDetector(
                onTap:
                    () =>
                        context
                            .read<HoldingPortfolioTableCubit>()
                            .toggleProfitLossDisplayMode(),
                child: Icon(
                  Icons.arrow_left,
                  size: 16,
                  color: vpColor.textDisabled,
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AutoSizeText(
                      state.profitLossDisplayMode ==
                              ProfitLossDisplayMode.absolute
                          ? VPTradingLocalize
                              .current
                              .trading_expected_profit_loss
                          : "% lãi lỗ",
                      style: context.textStyle.captionRegular?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                      maxLines: 1,
                      minFontSize: 10,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                    _buildSortIcon(
                      direction: sortDirection,
                      isProfitLoss: true,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 4),

              GestureDetector(
                onTap:
                    () =>
                        context
                            .read<HoldingPortfolioTableCubit>()
                            .toggleProfitLossDisplayMode(),
                child: Icon(
                  Icons.arrow_right,
                  size: 16,
                  color: vpColor.textDisabled,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSortIcon({
    required SortDirection direction,
    bool? isProfitLoss,
  }) {
    switch (direction) {
      case SortDirection.ascending:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CommonAssets.icons.icSortIncrease.svg(
              height: 4,
              colorFilter: ColorFilter.mode(vpColor.textBrand, BlendMode.srcIn),
            ),
            if (!(isProfitLoss == true))
              CommonAssets.icons.icSortDecrease.svg(
                height: 4,
                colorFilter: ColorFilter.mode(
                  vpColor.textDisabled,
                  BlendMode.srcIn,
                ),
              ),
          ],
        );
      case SortDirection.descending:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!(isProfitLoss == true))
              CommonAssets.icons.icSortIncrease.svg(
                height: 4,
                colorFilter: ColorFilter.mode(
                  vpColor.textDisabled,
                  BlendMode.srcIn,
                ),
              ),
            CommonAssets.icons.icSortDecrease.svg(
              height: 4,
              colorFilter: ColorFilter.mode(vpColor.textBrand, BlendMode.srcIn),
            ),
          ],
        );
      case SortDirection.none:
        if (isProfitLoss == true) {
          return CommonAssets.icons.icSortDecrease.svg(
            height: 4,
            colorFilter: ColorFilter.mode(
              vpColor.textDisabled,
              BlendMode.srcIn,
            ),
          );
        }
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CommonAssets.icons.icSortIncrease.svg(
              height: 4,
              colorFilter: ColorFilter.mode(
                vpColor.textDisabled,
                BlendMode.srcIn,
              ),
            ),
            CommonAssets.icons.icSortDecrease.svg(
              height: 4,
              colorFilter: ColorFilter.mode(
                vpColor.textDisabled,
                BlendMode.srcIn,
              ),
            ),
          ],
        );
    }
  }
}
