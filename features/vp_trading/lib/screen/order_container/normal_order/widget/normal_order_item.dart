import 'package:flutter/material.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/core/constant/stock_value_constans.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/widgets/vp_close_price_item_view.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/order_container/enum/new_order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/edit_command_bottom_sheet_wrapper.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/order_detail_bottomsheet.dart';
import 'package:vp_trading/utils/stock_utils.dart';

const List<int> _expandWidget = [12, 5, 6, 12];

class NormalOrderItem extends StatelessWidget {
  final OrderBookModel item;
  final ValueChanged<OrderBookModel> onEditCommand;
  final ValueChanged<OrderBookModel> onDeleteCommand;

  const NormalOrderItem({
    super.key,
    required this.item,
    required this.onEditCommand,
    required this.onDeleteCommand,
  });

  void openOrderDetailBottomSheet(BuildContext context) {
    VPPopup.bottomSheet(
      OrderDetailBottomSheet(
        model: item,
        onEditCommandSucces: () => onEditCommand(item),
        onDeleteCommand: () => onDeleteCommand(item),
      ),
    ).showSheet(context);
  }

  // Helper methods to check if actions are enabled
  bool get canEdit => item.allowAmend == StockAppConstants.y;
  bool get canDelete => item.allowCancel == StockAppConstants.y;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        openOrderDetailBottomSheet(context);
      },
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: vpColor.backgroundElevation0,
              borderRadius: BorderRadius.circular(16),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  flex: _expandWidget[0],
                  child: Row(
                    children: [
                      item.orderTypeEnum == OrderTypeEnum.sell
                          ? VpTradingAssets.icons.icConditionSell.svg()
                          : VpTradingAssets.icons.icConditionBuy.svg(),
                      const SizedBox(width: 16),
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AutoSizeText(
                              item.symbol ?? '',
                              style: vpTextStyle.subtitle14.copyColor(
                                vpColor.textPrimary,
                              ),
                              maxLines: 1,
                              minFontSize: 8,
                              overflow: TextOverflow.ellipsis,
                            ),
                            // Socket
                            IgnorePointer(
                              child: VPClosePriceItemView(
                                symbol: item.symbol ?? '',
                                initClosePrice: item.marketPrice,
                                initTextColor: item.colorPrice,
                                styleBuilder: (closePrice, color) {
                                  return vpTextStyle.captionMedium.copyColor(
                                    color ?? vpColor.textPrimary,
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: _expandWidget[1],
                  child: Column(
                    children: [
                      Text(
                        StockUtils.getQuotePrice(
                          num.tryParse(item.price ?? '0'),
                          item.priceType,
                        ),
                        style: vpTextStyle.captionRegular.copyColor(
                          vpColor.textPrimary,
                        ),
                        textAlign: TextAlign.end,
                      ),
                      Text(
                        item.execPrice?.getPriceFormatted(
                              convertToThousand: true,
                            ) ??
                            '0',
                        style: vpTextStyle.captionSemiBold.copyColor(
                          vpColor.textPrimary,
                        ),
                        textAlign: TextAlign.end,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: _expandWidget[2],
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      AutoSizeText(
                        MoneyUtils.formatMoney(
                          (item.qty ?? 0).toDouble(),
                          suffix: '',
                        ),
                        style: vpTextStyle.captionRegular.copyColor(
                          vpColor.textPrimary,
                        ),
                        maxLines: 1,
                        minFontSize: 10,
                      ),
                      const SizedBox(height: 4),
                      AutoSizeText(
                        MoneyUtils.formatMoney(
                          (item.execQty ?? 0).toDouble(),
                          suffix: '',
                        ),
                        style: vpTextStyle.captionSemiBold.copyColor(
                          vpColor.textPrimary,
                        ),
                        maxLines: 1,
                        minFontSize: 10,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: _expandWidget[3],
                  child: Column(
                    children: [
                      const SizedBox(height: 16),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          GestureDetector(
                            onTap: () {
                              if (canEdit) {
                                _showEditOrder(context);
                              }
                            },
                            child: Container(
                              margin: const EdgeInsets.only(right: 8),
                              decoration: BoxDecoration(
                                color:
                                    canEdit
                                        ? vpColor.backgroundElevation1
                                        : vpColor.backgroundElevation0,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: vpColor.textDisabled,
                                  width: 0.5,
                                ),
                              ),
                              padding: const EdgeInsets.all(8),
                              child: VpTradingAssets.icons.icEdit.svg(
                                color: canEdit ? null : vpColor.iconDisabled,
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 24,
                            child: VerticalDivider(
                              width: 1,
                              color: vpColor.textDisabled,
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              if (canDelete) {
                                dialogConfirmDeleteOrder(context, () {
                                  Navigator.of(context).pop();
                                  onDeleteCommand(item);
                                });
                              }
                            },
                            child: Container(
                              margin: const EdgeInsets.only(left: 8),
                              decoration: BoxDecoration(
                                color:
                                    canDelete
                                        ? vpColor.backgroundElevation1
                                        : vpColor.backgroundElevation0,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: vpColor.textDisabled,
                                  width: 0.5,
                                ),
                              ),
                              padding: const EdgeInsets.all(8),
                              child: VpTradingAssets.icons.icRemove2.svg(
                                color: canDelete ? null : vpColor.iconDisabled,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: item.newOrderStatusEnum.color.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(16),
                  bottomLeft: Radius.circular(4),
                  topLeft: Radius.circular(4),
                ),
                // border: Border.all(
                //   color: item.newOrderStatusEnum.color.withOpacity(0.3),
                //   width: 0.5,
                // ),
              ),
              child: Text(
                item.newOrderStatusEnum.label,
                style: vpTextStyle.caption2SemiBold?.copyWith(
                  color: item.newOrderStatusEnum.textColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showEditOrder(BuildContext context) {
    VPPopup.bottomSheet(
      EditCommandBottomSheetWrapper(
        model: item,
        onEditSuccess: () {
          context.pop();
          context.showSuccess(content: 'Đã sửa lệnh');
          onEditCommand(item);
        },
        onEditFailure: (message) {
          context.pop();
          context.showError(content: message);
        },
      ),
    ).showSheet(context);
  }
}
