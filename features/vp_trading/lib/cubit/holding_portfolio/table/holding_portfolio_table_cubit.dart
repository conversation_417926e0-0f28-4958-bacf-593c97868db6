import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/cubit/holding_portfolio/table/holding_portfolio_table_state.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';

class HoldingPortfolioTableCubit extends Cubit<HoldingPortfolioTableState> {
  HoldingPortfolioTableCubit() : super(const HoldingPortfolioTableState());

  /// Toggle the profit/loss display mode between absolute amount and percentage
  void toggleProfitLossDisplayMode() {
    final newMode =
        state.profitLossDisplayMode == ProfitLossDisplayMode.absolute
            ? ProfitLossDisplayMode.percentage
            : ProfitLossDisplayMode.absolute;

    emit(state.copyWith(profitLossDisplayMode: newMode));
  }

  /// Handle column sorting - cycles through none -> ascending -> descending -> none
  void onColumnSort(SortColumn column) {
    SortDirection newDirection;

    if (state.sortColumn != column) {
      // Different column selected, start with ascending
      newDirection = SortDirection.ascending;
    } else {
      // Same column, cycle through directions
      switch (state.sortDirection) {
        case SortDirection.none:
          newDirection = SortDirection.ascending;
          break;
        case SortDirection.ascending:
          newDirection = SortDirection.descending;
          break;
        case SortDirection.descending:
          newDirection = SortDirection.none;
          break;
      }
    }

    emit(
      state.copyWith(
        sortColumn: newDirection == SortDirection.none ? null : column,
        sortDirection: newDirection,
      ),
    );
  }

  /// Sort the portfolio list based on current sort settings
  List<HoldingPortfolioStockModel> sortPortfolioList(
    List<HoldingPortfolioStockModel> portfolioList,
  ) {
    if (state.sortColumn == null || state.sortDirection == SortDirection.none) {
      return portfolioList;
    }

    final sortedList = List<HoldingPortfolioStockModel>.from(portfolioList);

    sortedList.sort((a, b) {
      int comparison = 0;

      switch (state.sortColumn!) {
        case SortColumn.stockCode:
          comparison = (a.customSymbol).compareTo(b.customSymbol);
          break;
        case SortColumn.costPrice:
          comparison = (a.costPrice ?? 0).compareTo(b.costPrice ?? 0);
          break;
        case SortColumn.quantity:
          comparison = (a.total ?? 0).compareTo(b.total ?? 0);
          break;
        case SortColumn.profitLoss:
          comparison = a.pnlValue.compareTo(b.pnlValue);
          break;
      }

      return state.sortDirection == SortDirection.ascending
          ? comparison
          : -comparison;
    });

    return sortedList;
  }
}
