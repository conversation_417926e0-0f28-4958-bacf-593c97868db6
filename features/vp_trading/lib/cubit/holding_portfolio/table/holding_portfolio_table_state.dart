import 'package:equatable/equatable.dart';

enum SortColumn { stockCode, costPrice, quantity, profitLoss }

enum SortDirection { none, ascending, descending }

enum ProfitLossDisplayMode { absolute, percentage }

class HoldingPortfolioTableState extends Equatable {
  final SortColumn? sortColumn;
  final SortDirection sortDirection;
  final ProfitLossDisplayMode profitLossDisplayMode;

  const HoldingPortfolioTableState({
    this.sortColumn,
    this.sortDirection = SortDirection.none,
    this.profitLossDisplayMode = ProfitLossDisplayMode.absolute,
  });

  HoldingPortfolioTableState copyWith({
    SortColumn? sortColumn,
    SortDirection? sortDirection,
    ProfitLossDisplayMode? profitLossDisplayMode,
  }) {
    return HoldingPortfolioTableState(
      sortColumn: sortColumn ?? this.sortColumn,
      sortDirection: sortDirection ?? this.sortDirection,
      profitLossDisplayMode:
          profitLossDisplayMode ?? this.profitLossDisplayMode,
    );
  }

  @override
  List<Object?> get props => [sortColumn, sortDirection, profitLossDisplayMode];
}
